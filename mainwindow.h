#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTabWidget>
#include <QMessageBox>
#include <QFileDialog>
#include <QStandardPaths>
#include <QMap>
// 删除QtCharts引用，替换为LxChart
// #include <QtCharts>
#include "LxChart/lxmasschart.h"
#include "LxChart/lxticxicchart.h"
#include "LxChart/lxchartdata.h"
#include <QVector>
// 移除DataReader DLL依赖
// #include "FileData/datareader.h"
#include "FileData/dataprocessor.h"
#include "FileData/paramfilereader.h"
#include "FileData/filedatamanager.h"
#include "LxDataReader/lxdatareader.h"
#include "CustomControl/Dialogs/optionsdialog.h"
#include "Config/configmanager.h"
#include "Algorithm/PeakFind/peakfind.h"
#include "Config/PeakFindingParams.h"
#include "Config/PeakFindingConfigs.h"

// 前向声明数据分析类
class DataAnalysisWidget;

namespace Ui
{
    class MainWindow;
}

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

public slots:
    /**
     * @brief 重新积分所有图表中的曲线
     * 清除所有曲线的寻峰状态，使用最新的积分参数重新进行积分计算
     */
    void reintegrateAllCharts();

    /**
     * @brief 🎯 显示寻峰失败警告对话框
     * @param filePath 文件路径
     * @param dataType 数据类型（TIC/MASS/XIC）
     * @param errorMessage 错误信息
     */
    void showPeakFindingFailedWarning(const QString &filePath, const QString &dataType, const QString &errorMessage);

private:
    /**
     * @brief 初始化OptionsDialog配置系统
     * 检查OptionsDialog.xml是否存在，不存在则创建并使用原始UI参数
     */
    void initOptionsDialogConfig();

private slots:
    void on_btn_add_clicked();

private:
    Ui::MainWindow *ui;

    // 初始化函数
    void setupCharts();
    void connectAll();
    void setupUI();
    void init();                 // 初始化指针等一系列成员
    void loadMainWindowStyles(); // 加载MainWindow专用样式

    // 移除了示例项目的文件管理功能，当前项目不需要

    // 图表对象，使用LxChart替代原来的QChart
    LxMassChart *massChart;
    LxTicXicChart *ticXicChart; // 新的TIC/XIC组合图表

    // 文件管理类
    FileDataManager *fileDataManager = nullptr;
    LxDataReader *lxDataReader = nullptr;

    // 按照示例项目的设计添加多文件管理
    QMap<DataAnalysisWidget *, QString> mListDataAnalysis; // 最多9个文件
    DataAnalysisWidget *mDataAnalysis = nullptr;           // 主数据分析实例

private:
    OptionsDialog *optionDialog = nullptr;

protected:
    bool eventFilter(QObject *obj, QEvent *event) override;

signals:
    void sg_loadTIC(QString path); // 加载TIC数据
    void sg_windowStateChanged();
};

#endif // MAINWINDOW_H
