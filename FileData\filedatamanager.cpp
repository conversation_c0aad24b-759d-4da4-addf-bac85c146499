#include "filedatamanager.h"
#include <QCoreApplication>
#include <QThread>
#include <QTimer>
#include <QMutex>
#include <QMutexLocker>
// #include "LxChart/lxchart.h"
#include "Algorithm/PeakFind/wh_peakSearch.h"
#include "Algorithm/PeakFind/wh_signal_smothing.h"
#include "Algorithm/PeakFind/wh_baselineCorrect.h"
// 静态成员变量的初始化
QMap<QString, FileData *> FileDataManager::FileData_Map;
FileDataManager::FileDataManager(QObject *parent)
    : QObject{parent}, dataProcessor(new DataProcessor(this)), paramFileReader(new ParamFileReader(this)),
      lxDataReader(new LxDataReader(this))
{
    // qDebug() << "FileDataManager 构造，当前线程：" << QThread::currentThread();

    // 连接XIC处理完成信号到下一个请求处理槽（使用DirectConnection确保同步执行）
    connect(this, &FileDataManager::sg_xicProcessingCompleted, this, &FileDataManager::processNextXicRequest,
            static_cast<Qt::ConnectionType>(Qt::DirectConnection | Qt::UniqueConnection));

    qDebug() << "FileDataManager: XIC处理信号槽连接完成";
}

FileDataManager::~FileDataManager()
{
    qDebug() << "FileDataManager 析构";
}

void FileDataManager::setMaxThreadCount(int count)
{
    TaskManager::instance()->setMaxThreadCount(count);
}

int FileDataManager::maxThreadCount() const
{
    return TaskManager::instance()->maxThreadCount();
}

int FileDataManager::activeThreadCount() const
{
    return TaskManager::instance()->activeThreadCount();
}
void FileDataManager::showMassChart(QVector<std::tuple<QString, QPointF, int>> vecTuple)
{
    qDebug() << "FileDataManager::showMassChart: 收到" << vecTuple.size() << "个MASS加载请求";

    // 🎯 如果有多个MASS请求，启动批量模式
    if (vecTuple.size() > 1)
    {
        qDebug() << "FileDataManager::showMassChart: 多个MASS请求，启动批量模式";
        emit sg_startMassBatchLoading(vecTuple.size());
    }

    for (std::tuple<QString, QPointF, int> tuple : vecTuple)
    {
        loadMassDataForFrame(std::get<0>(tuple), std::get<1>(tuple), std::get<2>(tuple));
    }
}

// 旧的单个XIC加载方法已删除，使用showMultipleXicChart代替
/*
void FileDataManager::showXicChart(double mz)
{
    qDebug() << "FileDataManager::showXicChart: 为所有TIC在mz=" << mz << "位置加载XIC";

    // 遍历所有FileData，为每个都加载XIC
    for (auto it = FileData_Map.begin(); it != FileData_Map.end(); ++it)
    {
        FileData *fileData = it.value();
        if (fileData)
        {
            qDebug() << "FileDataManager::showXicChart: 为文件" << fileData->getFilePath() << "加载XIC";
            loadXicData(fileData, mz);
        }
    }
}
*/

void FileDataManager::showMultipleXicChart(QVector<std::tuple<QString, int, double>> xicRequests)
{
    qDebug() << "FileDataManager::showMultipleXicChart: 收到" << xicRequests.size() << "个XIC请求";

    // 按文件路径分组XIC请求，避免重复处理
    QMap<QString, QSet<double>> filePathToMzSet;
    for (const auto &request : xicRequests)
    {
        QString filePath = std::get<0>(request);
        double mz = std::get<2>(request);
        filePathToMzSet[filePath].insert(mz);
    }

    qDebug() << "FileDataManager::showMultipleXicChart: 分组后有" << filePathToMzSet.size() << "个文件需要处理XIC";

    // 如果当前正在处理XIC请求，跳过重复请求
    if (m_isProcessingXicRequests)
    {
        qDebug() << "FileDataManager::showMultipleXicChart: 当前正在处理XIC请求，跳过重复请求";
        return;
    }

    // 设置处理状态
    m_isProcessingXicRequests = true;

    // 如果有多个文件或多个mz值，启用批量模式
    int totalRequests = 0;
    for (auto it = filePathToMzSet.begin(); it != filePathToMzSet.end(); ++it)
    {
        totalRequests += it.value().size();
    }

    if (totalRequests > 1)
    {
        qDebug() << "FileDataManager::showMultipleXicChart: 多个XIC请求，启用批量模式";
        emit sg_beginBatchAdd();
    }

    // 为每个文件的每个mz值加载XIC
    for (auto it = filePathToMzSet.begin(); it != filePathToMzSet.end(); ++it)
    {
        QString filePath = it.key();
        QSet<double> mzSet = it.value();

        // 查找对应的FileData
        FileData *fileData = nullptr;
        for (auto fdIt = FileData_Map.begin(); fdIt != FileData_Map.end(); ++fdIt)
        {
            if (fdIt.value() && fdIt.value()->getFilePath() == filePath)
            {
                fileData = fdIt.value();
                break;
            }
        }

        if (fileData)
        {
            // 为该文件的所有mz值加载XIC
            for (double mz : mzSet)
            {
                qDebug() << "FileDataManager::showMultipleXicChart: 为文件" << filePath << "加载XIC，m/z:" << mz;
                loadXicData(fileData, mz);
            }
        }
        else
        {
            qDebug() << "FileDataManager::showMultipleXicChart: 找不到文件对应的FileData:" << filePath;
        }
    }

    // 结束批量模式
    if (totalRequests > 1)
    {
        emit sg_endBatchAdd();
    }

    // 重置处理状态
    m_isProcessingXicRequests = false;
    qDebug() << "FileDataManager::showMultipleXicChart: XIC处理完成";
}

void FileDataManager::processNextXicRequest()
{
    // 这个方法现在已经不再使用，因为XIC请求在showMultipleXicChart中直接处理
    // 保留这个方法是为了兼容性，避免编译错误
    qDebug() << "FileDataManager::processNextXicRequest: 该方法已废弃，XIC请求现在在showMultipleXicChart中直接处理";
}

void FileDataManager::getAvgMass(QVector<std::tuple<QString, int, QVector<double>>> vec)
{
    totalAvgLoadCount = vec.size();
    qDebug() << "=== 开始计算平均质谱 ===";
    qDebug() << "主线程调用getAvgMass，当前线程:" << QThread::currentThread();
    qDebug() << "需要处理的TIC曲线数量:" << totalAvgLoadCount;

    // 检查当前是否已经在计算中
    if (AvgMassManager::getAvgMassStatus() == GlobalEnums::AvgMassStatus::Calculating)
    {
        qDebug() << "❌ 平均质谱正在计算中，跳过新的计算请求";
        return;
    }

    // 检查是否为增量计算（只有一个TIC且avgMassMap不为空）
    bool isIncrementalCalculation = (vec.size() == 1 && !AvgMassManager::avgMassMap.isEmpty());

    if (isIncrementalCalculation)
    {
        qDebug() << "=== 增量计算模式：只计算新增的TIC ===";
        QString newTicPath = std::get<0>(vec.at(0));
        int newTicEventId = std::get<1>(vec.at(0));
        qDebug() << "新增TIC - 文件:" << newTicPath << "，事件ID:" << newTicEventId;

        // 检查该TIC是否已存在平均质谱数据
        if (AvgMassManager::containsAvgMass(newTicPath, newTicEventId))
        {
            qDebug() << "该TIC已存在平均质谱数据，跳过计算";
            return;
        }
    }
    else
    {
        qDebug() << "=== 全量计算模式：清除现有数据并重新计算所有TIC ===";
        AvgMassManager::clearAvgMassMap();
    }

    // 设置为计算状态
    AvgMassManager::setAvgMassStatus(GlobalEnums::AvgMassStatus::Calculating);

    for (int i = 0; i < vec.size(); i++)
    {
        QString path = get<0>(vec.at(i));
        int eventId = get<1>(vec.at(i));
        QVector<double> vecPoint = get<2>(vec.at(i));
        qDebug() << "搜索" << i;

        FileData *data = FileData_Map.find(path).value();
        if (!data)
        {
            continue;
        }
        loadMassDataForFrameWithAvg(path, vecPoint, eventId, data);
    }
}

void FileDataManager::slot_sg_TIC2XIC_Clicked(QString path)
{
    auto it = FileData_Map.find(path);
    if (!FileData_Map.contains(path))
    {
        qDebug() << "不包含" << path;
        return;
    }

    FileData *dataPtr = it.value();
    QString filePath = dataPtr->getFilePath(); // 获取路径用于验证

    TaskManager::instance()->runNamed("TIC2XIC", [this, dataPtr, filePath]()
                                      {
        // 检查FileData是否仍然有效
        if (!dataPtr || !FileData_Map.contains(filePath) || FileData_Map[filePath] != dataPtr) {
            qDebug() << "FileDataManager::slot_sg_TIC2XIC_Clicked: FileData已无效，跳过TIC2XIC处理";
            return;
        }

        AvgMassManager::instance()->TIC2XIC(*dataPtr); });
}

bool FileDataManager::removeData(QString path)
{
    qDebug() << "FileDataManager::removeData: 方法开始，路径:" << path;

    if (!FileData_Map.contains(path))
    {
        qDebug() << "FileDataManager::removeData: 路径不存在于映射中:" << path;
        return false;
    }

    qDebug() << "FileDataManager::removeData: 找到路径，开始移除:" << path;

    // 获取FileData指针并删除对象
    FileData *fileData = FileData_Map.value(path);
    if (fileData)
    {
        qDebug() << "FileDataManager::removeData: 准备删除FileData对象，路径:" << path << "，指针:" << fileData;

        // 检查指针有效性
        try
        {
            // 尝试访问对象的成员，检查对象是否有效
            QString testPath = fileData->getFilePath();
            qDebug() << "FileDataManager::removeData: FileData对象有效，路径验证:" << testPath;

            qDebug() << "FileDataManager::removeData: 开始删除FileData对象，当前线程:" << QThread::currentThread();

            // 检查是否在主线程中
            if (QThread::currentThread() == QCoreApplication::instance()->thread())
            {
                // 在主线程中，直接删除
                delete fileData;
                qDebug() << "FileDataManager::removeData: FileData对象已在主线程中删除";
            }
            else
            {
                // 不在主线程中，使用deleteLater确保在主线程中删除
                qDebug() << "FileDataManager::removeData: 不在主线程中，使用deleteLater";
                fileData->deleteLater();
                qDebug() << "FileDataManager::removeData: FileData对象已调用deleteLater";
            }

            // 确保指针被置空
            fileData = nullptr;
            qDebug() << "FileDataManager::removeData: FileData指针已置空";
        }
        catch (const std::exception &e)
        {
            qDebug() << "FileDataManager::removeData: 删除FileData时发生异常:" << e.what();
        }
        catch (...)
        {
            qDebug() << "FileDataManager::removeData: 删除FileData时发生未知异常";
        }
    }
    else
    {
        qDebug() << "FileDataManager::removeData: FileData指针为空，路径:" << path;
    }

    // 从映射中移除
    qDebug() << "FileDataManager::removeData: 从映射中移除路径:" << path;
    FileData_Map.remove(path);
    qDebug() << "FileDataManager::removeData: 映射移除完成，当前映射大小:" << FileData_Map.size();

    qDebug() << "FileDataManager::removeData: 方法完成，返回true";
    return true;
}

QColor FileDataManager::getOriginColorByEvent(QString path, int eventId)
{
    if (!FileData_Map.contains(path))
    {
        return QColor();
    }
    auto it = FileData_Map.find(path).value();
    if (it->TicMap.contains(eventId))
    {
        return it->TicMap.find(eventId).value()->getOriginalColor();
    }
    return QColor();
}

FileData *FileDataManager::generateFileData(QString path)
{
    // qDebug() << __FUNCTION__ << "当前线程：" << QThread::currentThread();

    // 创建FileData指针
    FileData *data = new FileData(path);

    // 使用线程池异步加载TIC数据，使用值捕获避免数据竞争
    TaskManager::instance()->runNamed("加载TIC数据", [this, data, path]()
                                      {
        // qDebug() << "正在加载文件: " << path << "，线程:" << QThread::currentThread();

        //*测试代码，随时可删除 */////////////⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️///////////////

        // if (lxDataReader->loadFileTicFullPage(data)) {
        //     // data->TICChartData->setTitle(data->qstr_filePath());

        //     // 使用事件循环安全地发出信号和更新UI相关的操作
        //     QMetaObject::invokeMethod(
        //         this,
        //         [this, data]() {
        //             emit sg_sendTicChartData(data);
        //             FileData_Map.insert(data->getFilePath(), data);
        //             qDebug() << data->getFilePath() << "读取成功";
        //         },
        //         Qt::QueuedConnection);
        // } else {
        //     qDebug() << "TIC数据加载失败";
        // }
        //*测试代码，随时可删除 */////////////⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️///////////////

        // 使用统一数据读取器（完全替代DLL）
        if (lxDataReader->loadTICDataComplete(data)) {
            // qDebug() << "✅ 使用LxDataReader成功读取TIC数据";

            // 更新每个TIC数据的扫描模式
            updateTicScanModes(data);

            // 🎯 新流程：数据加载完成后立即进行寻峰
            bool peakFindingSuccess = performPeakFindingForFileData(data);

            if (peakFindingSuccess) {
                // 寻峰成功后发送信号给图表
                QMetaObject::invokeMethod(
                    this,
                    [this, data]() {
                        // 发送已完成寻峰的数据给UI
                        emit sg_sendTicChartData(data);
                        qDebug() << data->getFilePath() << "TIC数据读取和寻峰成功";
                    },
                    Qt::QueuedConnection);
            } else {
                qDebug() << "❌ TIC数据寻峰失败，但仍发送数据给UI";
                // 即使寻峰失败，也发送数据给UI，让用户看到数据
                QMetaObject::invokeMethod(
                    this,
                    [this, data]() {
                        emit sg_sendTicChartData(data);
                        // 🎯 发送寻峰失败警告信号
                        emit sg_peakFindingFailed(data->getFilePath(), "TIC", "TIC数据寻峰失败，请检查数据质量或重新添加文件");
                    },
                    Qt::QueuedConnection);
            }
        } else {
            qDebug() << "❌ LxDataReader读取失败";
            // 不再回退到DLL读取器，直接报告失败
        } });

    return data;
}

void FileDataManager::confirmAddFileData(FileData *data)
{
    if (!data)
    {
        qDebug() << "FileDataManager::confirmAddFileData: data为空";
        return;
    }

    QString filePath = data->getFilePath();

    // 检查是否已经存在（避免重复添加）
    if (FileData_Map.contains(filePath))
    {
        qDebug() << "FileDataManager::confirmAddFileData: FileData已存在，路径:" << filePath;
        return;
    }

    // 添加到管理器
    FileData_Map.insert(filePath, data);
    qDebug() << "FileDataManager::confirmAddFileData: ✅ FileData已添加到管理器，路径:" << filePath;
}

void FileDataManager::updateTicScanModes(FileData *data)
{
    if (!data || !lxDataReader)
    {
        qDebug() << "FileDataManager::updateTicScanModes: 无效的data或lxDataReader指针";
        return;
    }

    // 获取事件数量
    int eventCount = lxDataReader->getEventCount(*data);
    if (eventCount <= 0)
    {
        qDebug() << "FileDataManager::updateTicScanModes: 没有有效的事件数据";
        return;
    }

    qDebug() << "FileDataManager::updateTicScanModes: 开始更新" << eventCount << "个事件的扫描模式";

    // 遍历所有事件，更新对应TIC数据的扫描模式
    for (int eventId = 0; eventId < eventCount; ++eventId)
    {
        // 获取该事件的扫描模式
        GlobalEnums::ScanMode scanMode = lxDataReader->getScanModeForEvent(*data, eventId);

        // 获取对应的TIC数据
        TicChartData *ticData = data->getTicData(eventId);
        if (ticData)
        {
            // 更新扫描模式
            ticData->setScanMode(scanMode);
            qDebug() << "FileDataManager::updateTicScanModes: 更新事件" << eventId
                     << "的扫描模式为:" << (int)scanMode;
        }
        else
        {
            qDebug() << "FileDataManager::updateTicScanModes: 事件" << eventId << "没有对应的TIC数据";
        }
    }
}

void FileDataManager::loadMassDataForFrame(QString paramPath, const QPointF &point, int eventId)
{
    qDebug() << __FUNCTION__ << "当前线程：" << QThread::currentThread();

    // 使用线程池异步处理，使用值捕获
    TaskManager::instance()->runNamed("加载Mass数据帧", [this, paramPath, point, eventId]()
                                      {
        qDebug() << "开始为" << paramPath << "加载Mass数据，线程:" << QThread::currentThread();

        FileData *data = nullptr;
        // 安全地获取FileData指针
        QMetaObject::invokeMethod(
            this,
            [this, paramPath, &data]() {
                qDebug() << "查找指针:" << paramPath;
                for (auto it = FileData_Map.begin(); it != FileData_Map.end(); it++) {
                    qDebug() << "key:" << it.key();
                }
                data = FileData_Map.value(paramPath);
            },
            Qt::BlockingQueuedConnection);

        if (!data) {
            qDebug() << "找不到data指针";
            return;
        }

        // 复制必要数据避免线程间访问
        QVector<double> timePointsCopy;
        QVector<qint64> frameIndicesCopy;

        QMetaObject::invokeMethod(
            this,
            [data, &timePointsCopy, &frameIndicesCopy]() {
                timePointsCopy = data->timePoints;
                frameIndicesCopy = data->frameIndices;
            },
            Qt::BlockingQueuedConnection);

        int frameIndex = findIndex(timePointsCopy, point.x());
        qDebug() << "帧索引:" << frameIndex;
        qDebug() << "点击时间:" << point.x() << "秒";
        qDebug() << "timePoints数组大小:" << timePointsCopy.size();
        qDebug() << "frameIndices数组大小:" << frameIndicesCopy.size();

        if (frameIndex < 0 || frameIndex >= frameIndicesCopy.size()) {
            qDebug() << "无效的帧索引: " << frameIndex;
            return;
        }

        // 获取实际的帧索引值
        qint64 frameIndexValue = frameIndicesCopy[frameIndex];
        qDebug() << "使用帧索引:" << frameIndex << "，偏移量:" << frameIndexValue;

        // 🎯 修复：直接使用双击获取的帧，如果无效则提示错误
        if (frameIndexValue <= 0) {
            qDebug() << "❌ 双击获取的帧偏移量无效，帧索引:" << frameIndex << "，偏移量:" << frameIndexValue;
            qDebug() << "   这通常表示数据文件有问题或帧索引计算错误";
            return;
        }

        // 使用统一读取器完整加载MASS数据（完全替代DLL）
        bool success = lxDataReader->loadMassDataComplete(data, frameIndex, eventId);

        if (success) {
            // qDebug() << "✅ 使用LxDataReader成功加载完整MASS数据，帧索引:" << frameIndex;

            // 🎯 新流程：MASS数据加载完成后立即进行寻峰
            bool peakFindingSuccess = performPeakFindingForMassData(data, eventId, frameIndex);

            QMetaObject::invokeMethod(
                this,
                [this, data, frameIndexValue, eventId, peakFindingSuccess]() {
                    // 发送已完成寻峰的MASS数据给图表
                    emit sg_sendMassChartData(eventId, frameIndexValue, data);
                    if (!peakFindingSuccess) {
                        qDebug() << "⚠️ MASS数据寻峰失败，但仍发送数据给UI";
                        // 🎯 发送寻峰失败警告信号
                        emit sg_peakFindingFailed(data->getFilePath(), "MASS",
                            QString("MASS数据寻峰失败（事件ID:%1，帧索引:%2），请检查数据质量或重新添加文件")
                            .arg(eventId).arg(frameIndexValue));
                    }
                },
                Qt::QueuedConnection);
        } else {
            qDebug() << "❌ LxDataReader加载完整MASS数据失败，帧索引:" << frameIndex;
            qDebug() << "   indexArray大小:" << data->getIndexArray().size();
            qDebug() << "   frameIndex:" << frameIndex;
            qDebug() << "   eventId:" << eventId;
        } });
}

void FileDataManager::loadMassDataForFrameWithAvg(QString paramPath, QVector<double> pointVec, int eventId, FileData *data)
{
    qDebug() << "=== 开始加载平均质谱数据 ===";
    qDebug() << "loadMassDataForFrameWithAvg调用线程：" << QThread::currentThread();
    qDebug() << "文件路径:" << paramPath;
    qDebug() << "事件ID:" << eventId;
    qDebug() << "背景区域数据点数量:" << pointVec.size();

    if (!AvgMassManager::isRefExist)
    {
        qDebug() << "❌ 背景区域不存在，不需要计算平均质谱";
        return;
    }

    // 严格检查FileData指针和数据有效性
    if (!data)
    {
        qDebug() << "❌ FileData指针为空，无法获取数据";
        AvgMassManager::setAvgMassStatus(GlobalEnums::AvgMassStatus::Stop);
        return;
    }

    // 验证FileData对象是否在FileData_Map中存在（确保未被删除）
    // 由于FileData的添加可能是异步的，给一些时间让映射完成
    if (!FileData_Map.contains(paramPath) || FileData_Map.value(paramPath) != data)
    {
        qDebug() << "⚠️ FileData对象暂时不在映射中，可能正在异步添加，等待后重试";
        qDebug() << "   - 路径:" << paramPath;
        qDebug() << "   - 映射中是否存在:" << FileData_Map.contains(paramPath);
        qDebug() << "   - 指针是否匹配:" << (FileData_Map.contains(paramPath) ? (FileData_Map.value(paramPath) == data) : false);

        // 延迟重试，给FileData映射添加时间
        QTimer::singleShot(500, this, [this, paramPath, pointVec, eventId, data]()
                           {
            qDebug() << "🔄 重新尝试加载平均质谱数据，路径:" << paramPath;

            // 重新验证FileData映射
            if (!FileData_Map.contains(paramPath)) {
                qDebug() << "❌ 延迟重试后FileData仍不在映射中，放弃计算平均质谱";
                AvgMassManager::setAvgMassStatus(GlobalEnums::AvgMassStatus::Stop);
                return;
            }

            FileData *currentData = FileData_Map.value(paramPath);
            if (!currentData) {
                qDebug() << "❌ 延迟重试后FileData指针为空，放弃计算平均质谱";
                AvgMassManager::setAvgMassStatus(GlobalEnums::AvgMassStatus::Stop);
                return;
            }

            qDebug() << "✅ 延迟重试成功，FileData已在映射中，继续计算平均质谱";

            // 使用映射中的FileData指针重新调用
            loadMassDataForFrameWithAvg(paramPath, pointVec, eventId, currentData); });

        return;
    }

    // 使用线程池异步处理，使用引用避免大量数据复制
    TaskManager::instance()->runNamed("加载Mass数据帧ForAvg", [this, paramPath, pointVec, eventId, data]()
                                      {
        qDebug() << "📊 TaskManager工作线程开始处理平均质谱数据";
        qDebug() << "工作线程ID:" << QThread::currentThread();
        qDebug() << "处理文件:" << paramPath << "，事件ID:" << eventId;

        // 在工作线程中安全获取FileData指针
        FileData *workData = nullptr;
        QMetaObject::invokeMethod(this, [this, paramPath, &workData]() {
            workData = FileData_Map.value(paramPath);
        }, Qt::BlockingQueuedConnection);

        if (!workData) {
            qDebug() << "❌ 在工作线程中找不到FileData指针:" << paramPath;
            return;
        }

        // 直接在工作线程中计算帧索引，使用引用避免复制
        QVector<int> frameIndexVec;
        frameIndexVec.reserve(pointVec.size());

        // 使用引用访问FileData数据，避免复制
        const QVector<double> &timePoints = workData->timePoints;
        const QVector<qint64> &frameIndices = workData->frameIndices;

        qDebug() << "📊 数据数组大小检查:";
        qDebug() << "   timePoints数组大小:" << timePoints.size();
        qDebug() << "   frameIndices数组大小:" << frameIndices.size();
        qDebug() << "   背景区域时间点数量:" << pointVec.size();

        if (!timePoints.isEmpty()) {
            qDebug() << "   时间范围:" << timePoints.first() << "~" << timePoints.last();
        }

        foreach (double x, pointVec) {
            int timeIndex = findIndex(timePoints, x);  // 这是时间点在timePoints中的索引位置

            qDebug() << "🔍 查找时间点" << x << "在timePoints中的索引:" << timeIndex;

            if (timeIndex >= 0 && timeIndex < frameIndices.size()) {
                frameIndexVec.append(timeIndex);  // 使用时间索引作为帧索引
                qDebug() << "✅ 时间点" << x << "→ 时间索引:" << timeIndex << "→ 帧偏移:" << frameIndices[timeIndex];
            } else {
                qDebug() << "❌ 无效的时间索引:" << timeIndex << "，时间点:" << x
                         << "，timePoints大小:" << timePoints.size()
                         << "，frameIndices大小:" << frameIndices.size();
            }
        }

        qDebug() << "✅ 帧索引计算完成，有效索引数量:" << frameIndexVec.size();

        if (frameIndexVec.isEmpty()) {
            qDebug() << "❌ 没有有效的帧索引，跳过平均质谱计算";
            return;
        }

        qDebug() << "✅ 开始加载平均质谱数据，帧索引数量:" << frameIndexVec.size();
        // 使用统一读取器加载平均质谱数据
        bool success = lxDataReader->loadMassDataForAvg(eventId, frameIndexVec, workData);
        qDebug() << "📊 平均质谱数据加载" << (success ? "成功" : "失败");

        QMetaObject::invokeMethod(
            this,
            [this, workData, success, eventId]() {
                if (success) {
                    QMutex mutex;
                    mutex.lock();
                    totalAvgLoadCount--;
                    mutex.unlock();

                    qDebug() << "加载完成" << eventId << totalAvgLoadCount;
                    if (totalAvgLoadCount == 0) {
                        qDebug() << "全部平均质谱加载完成\n-------------------------";

                        TaskManager::instance()->run([=]() {
                            qDebug() << "=== 平均质谱计算完成，开始处理数据 ===";
                            qDebug() << "平均质谱计算线程:" << QThread::currentThread();

                            int totalFiles = AvgMassManager::avgMassMap.size();
                            int totalEvents = 0;

                            for (auto it = AvgMassManager::avgMassMap.begin(); it != AvgMassManager::avgMassMap.end(); it++) {
                                QString filePath = it.key();
                                qDebug() << "\n【文件路径】:" << filePath;

                                for (auto it2 = it.value().begin(); it2 != it.value().end(); it2++) {
                                    int eventId = it2.key();
                                    totalEvents++;

                                    qDebug() << "  【事件ID】:" << eventId;
                                    std::tuple<QVector<double>, QVector<double>, bool, int> &tup = it2.value();
                                    QVector<double> &vecX = std::get<0>(tup);
                                    QVector<double> &vecY = std::get<1>(tup);

                                    bool &flag = std::get<2>(tup);
                                    int &num = std::get<3>(tup);

                                    if (num <= 0) {
                                        qDebug() << "    ❌ 错误：平均数量小于等于0，num =" << num;
                                        continue;
                                    }

                                    qDebug() << "    📊 平均质谱统计信息:";
                                    qDebug() << "      - 数据点数量:" << vecX.size();
                                    qDebug() << "      - 参与平均的质谱数量:" << num;
                                    qDebug() << "      - X轴是否初始化:" << flag;

                                    // 执行平均计算
                                    for (int i = 0; i < vecY.size(); i++) {
                                        double before = vecY[i];
                                        vecY[i] /= num;
                                    }

                                    // 统计零值数量
                                    int zeroCount = 0;
                                    foreach (double x, vecX) {
                                        if (x == 0) {
                                            ++zeroCount;
                                        }
                                    }

                                    if (!vecX.isEmpty() && !vecY.isEmpty()) {
                                        qDebug() << "    📈 平均质谱数据范围:";
                                        qDebug() << "      - X轴范围: [" << vecX.first() << ", " << vecX.last() << "]";
                                        qDebug() << "      - Y轴范围: [" << *std::min_element(vecY.begin(), vecY.end()) << ", "
                                                 << *std::max_element(vecY.begin(), vecY.end()) << "]";
                                        qDebug() << "      - 零值数量:" << zeroCount;
                                    }

                                    qDebug() << "    ✅ 事件" << eventId << "的平均质谱计算完成";
                                }
                            }

                            qDebug() << "\n=== 平均质谱处理总结 ===";
                            qDebug() << "处理文件数量:" << totalFiles;
                            qDebug() << "处理事件数量:" << totalEvents;
                            qDebug() << "平均质谱状态设置为Ready";

                            AvgMassManager::setAvgMassStatus(GlobalEnums::AvgMassStatus::Ready);
                        });
                    }
                } else {
                    // 有任何一个没加载成功或者停止计算了则立刻结束
                    qDebug() << workData->getFilePath() << "加载失败  事件号:" << eventId;
                    AvgMassManager::clearAvgMassMap();
                    // 加载失败后改为停止计算状态
                    AvgMassManager::setAvgMassStatus(GlobalEnums::AvgMassStatus::Stop);
                    return;
                }
                // if (success) {
                //     emit sg_sendMassChartData(eventId, frameIndexValue, data);
                // }
            },
            Qt::QueuedConnection); });
}

void FileDataManager::loadXicData(FileData *data, double mz)
{
    if (!data)
    {
        qDebug() << "FileDataManager::loadXicData: FileData指针为空";
        return;
    }

    QString filePath = data->getFilePath();
    qDebug() << "FileDataManager::loadXicData: 为文件" << filePath << "在mz=" << mz << "位置加载所有TIC的XIC";

    TaskManager::instance()->runNamed("加载XIC数据", [this, data, mz, filePath]()
                                      {
        qDebug() << "FileDataManager::loadXicData: 开始加载XIC数据，当前线程：" << QThread::currentThread();
        qDebug() << "FileDataManager::loadXicData: 路径:" << filePath << "，m/z:" << mz;

        // 检查FileData指针是否仍然有效
        if (!FileData_Map.contains(filePath) || FileData_Map[filePath] != data) {
            qDebug() << "FileDataManager::loadXicData: FileData指针无效或已被移除，跳过处理";
            return;
        }

        // 遍历该FileData的所有TIC，为每个TIC创建XIC
        QList<int> eventIds = data->getAllEventIds();
        qDebug() << "FileDataManager::loadXicData: 找到" << eventIds.size() << "个TIC数据";

        for (int eventId : eventIds) {
            TicChartData* ticData = data->getTicData(eventId);
            if (!ticData) continue;

            qDebug() << "FileDataManager::loadXicData: 开始为事件ID" << eventId << "加载XIC数据，m/z:" << mz;

            // 直接使用LxDataReader加载XIC数据，它会内部创建XIC对象并设置数据
            bool xicSuccess = lxDataReader->loadXicDataBatch(data, eventId, mz);
            if (xicSuccess) {
                // 验证数据是否正确设置
                XicChartData *latestXic = ticData->getLatestXicData();
                if (latestXic) {
                    QVector<QPointF> xicDataPoints = latestXic->getData();
                    qDebug() << "FileDataManager::loadXicData: XIC数据加载成功，事件ID:" << eventId
                             << "，数据点数:" << xicDataPoints.size()
                             << "，XIC总数:" << ticData->getXicDataList().size();

                    // 验证数据内容
                    if (!xicDataPoints.isEmpty()) {
                        qDebug() << "   第一个数据点:" << xicDataPoints.first();
                        qDebug() << "   最后一个数据点:" << xicDataPoints.last();
                        qDebug() << "   数据范围: X[" << latestXic->getMinX() << "~" << latestXic->getMaxX()
                                 << "] Y[" << latestXic->getMinY() << "~" << latestXic->getMaxY() << "]";
                    } else {
                        qDebug() << "   警告：XIC数据点为空！";
                    }
                } else {
                    qDebug() << "FileDataManager::loadXicData: XIC数据加载成功但没有找到最新XIC，事件ID:" << eventId;
                }
            } else {
                qDebug() << "FileDataManager::loadXicData: XIC数据加载失败，事件ID:" << eventId;
            }
        }

        // 确保所有XIC数据都已正确设置后再发送信号
        QThread::msleep(10); // 短暂等待，确保数据设置完成

        // 🎯 新流程：XIC数据加载完成后立即进行寻峰
        bool peakFindingSuccess = performPeakFindingForXicData(data, mz);

        // 发送XIC数据（这个信号会在主线程中处理UI更新）
        emit sg_sendXicChartData(data);
        qDebug() << "FileDataManager::loadXicData: sg_sendXicChartData信号已发射，文件:" << filePath;

        if (!peakFindingSuccess) {
            qDebug() << "⚠️ XIC数据寻峰失败，但仍发送数据给UI";
            // 🎯 发送寻峰失败警告信号
            emit sg_peakFindingFailed(data->getFilePath(), "XIC",
                QString("XIC数据寻峰失败（m/z:%1），请检查数据质量或重新添加文件").arg(mz));
        }

        // 发射XIC处理完成信号，触发下一个请求的处理
        emit sg_xicProcessingCompleted();
        qDebug() << "FileDataManager::loadXicData: sg_xicProcessingCompleted信号已发射"; });
}

bool FileDataManager::performPeakFindingForFileData(FileData *data)
{
    if (!data)
    {
        qDebug() << "FileDataManager::performPeakFindingForFileData: FileData指针为空";
        return false;
    }

    qDebug() << "FileDataManager::performPeakFindingForFileData: 开始为FileData进行寻峰，路径:" << data->getFilePath();

    bool overallSuccess = true;
    int processedCount = 0;
    int skippedCount = 0;
    int failedCount = 0;

    // 获取所有TIC数据
    QList<int> eventIds = data->getAllEventIds();
    qDebug() << "FileDataManager::performPeakFindingForFileData: 找到" << eventIds.size() << "个TIC数据";

    for (int eventId : eventIds)
    {
        TicChartData *ticData = data->getTicData(eventId);
        if (!ticData)
        {
            qDebug() << "FileDataManager::performPeakFindingForFileData: 找不到事件ID" << eventId << "的TIC数据";
            skippedCount++;
            continue;
        }

        // 检查是否已经寻峰
        if (ticData->getHasFindPeak())
        {
            qDebug() << "FileDataManager::performPeakFindingForFileData: 事件ID" << eventId << "已完成寻峰，跳过";
            skippedCount++;
            continue;
        }

        // 检查是否为MRM类型的数据，MRM数据不需要寻峰
        if (ticData->shouldUseBarChart())
        {
            qDebug() << "FileDataManager::performPeakFindingForFileData: 事件ID" << eventId << "为MRM类型，跳过寻峰";
            ticData->setHasFindPeak(true);
            skippedCount++;
            continue;
        }

        try
        {
            // 🎯 数据验证：检查数据完整性
            QVector<double> dataX = ticData->getDataX();
            QVector<double> dataY = ticData->getDataY();

            // 🎯 边界检查：验证数据大小
            if (dataX.isEmpty() || dataY.isEmpty())
            {
                qDebug() << "FileDataManager::performPeakFindingForFileData: 事件ID" << eventId << "数据为空，跳过寻峰";
                ticData->setHasFindPeak(true);
                skippedCount++;
                continue;
            }

            if (dataX.size() != dataY.size())
            {
                qDebug() << "FileDataManager::performPeakFindingForFileData: 事件ID" << eventId << "X和Y数据大小不匹配，跳过寻峰"
                         << "，X大小:" << dataX.size() << "，Y大小:" << dataY.size();
                ticData->setHasFindPeak(true);
                skippedCount++;
                continue;
            }

            if (dataX.size() < 3)
            {
                qDebug() << "FileDataManager::performPeakFindingForFileData: 事件ID" << eventId << "数据点太少，无法寻峰"
                         << "，数据点数:" << dataX.size();
                ticData->setHasFindPeak(true);
                skippedCount++;
                continue;
            }

            // 使用默认参数进行寻峰
            std::vector<double> originalData = dataY.toStdVector();
            std::vector<double> correctedData = cubicBaselineCorrection(movingAverage(originalData, 2));

            // 调用寻峰算法
            ticData->peakVec = searchPeaks(originalData);

            // 🎯 安全的峰数据处理：添加边界检查
            for (int i = 0; i < ticData->peakVec.size(); i++)
            {
                Peak &p = ticData->peakVec[i];

                // 验证峰索引的有效性
                if (p.top < 0 || p.top >= dataX.size() || p.top >= dataY.size())
                {
                    qDebug() << "FileDataManager::performPeakFindingForFileData: 峰顶索引越界，峰" << i << "，top:" << p.top
                             << "，数据大小:" << dataX.size();
                    continue;
                }
                if (p.start < 0 || p.start >= dataX.size() || p.start >= dataY.size())
                {
                    qDebug() << "FileDataManager::performPeakFindingForFileData: 峰起始索引越界，峰" << i << "，start:" << p.start
                             << "，数据大小:" << dataX.size();
                    continue;
                }
                if (p.end < 0 || p.end >= dataX.size() || p.end >= dataY.size())
                {
                    qDebug() << "FileDataManager::performPeakFindingForFileData: 峰结束索引越界，峰" << i << "，end:" << p.end
                             << "，数据大小:" << dataX.size();
                    continue;
                }

                // 安全地设置峰点坐标
                p.pTop = QPointF(dataX.at(p.top), dataY.at(p.top));
                p.pStart = QPointF(dataX.at(p.start), dataY.at(p.start));
                p.pEnd = QPointF(dataX.at(p.end), dataY.at(p.end));
            }

            // 计算SNR
            if (!ticData->peakVec.empty())
            {
                std::vector<double> snrList = CalcSNRofPeakList(correctedData, ticData->peakVec, 10);
                for (int i = 0; i < ticData->peakVec.size() && i < snrList.size(); i++)
                {
                    ticData->peakVec[i].snr = snrList[i];
                }
            }

            // 标记为已寻峰
            ticData->setHasFindPeak(true);
            processedCount++;

            qDebug() << "FileDataManager::performPeakFindingForFileData: 事件ID" << eventId << "寻峰完成，峰数量:" << ticData->peakVec.size();
        }
        catch (const std::exception &e)
        {
            qDebug() << "FileDataManager::performPeakFindingForFileData: 事件ID" << eventId << "寻峰过程中发生异常:" << e.what();
            ticData->setHasFindPeak(true); // 标记为已处理，避免重复尝试
            failedCount++;
            overallSuccess = false;
        }
        catch (...)
        {
            qDebug() << "FileDataManager::performPeakFindingForFileData: 事件ID" << eventId << "寻峰过程中发生未知异常";
            ticData->setHasFindPeak(true); // 标记为已处理
            failedCount++;
            overallSuccess = false;
        }
    }

    qDebug() << "FileDataManager::performPeakFindingForFileData: 寻峰完成，处理:" << processedCount
             << "，跳过:" << skippedCount << "，失败:" << failedCount;

    return overallSuccess && failedCount == 0;
}

bool FileDataManager::performPeakFindingForMassData(FileData *data, int eventId, int frameIndex)
{
    if (!data)
    {
        qDebug() << "FileDataManager::performPeakFindingForMassData: FileData指针为空";
        return false;
    }

    qDebug() << "FileDataManager::performPeakFindingForMassData: 开始为MASS数据进行寻峰，事件ID:" << eventId << "，帧索引:" << frameIndex;

    // 获取MASS数据
    MassChartData *massData = data->getMassData(eventId, frameIndex);
    if (!massData)
    {
        qDebug() << "FileDataManager::performPeakFindingForMassData: 找不到MASS数据，事件ID:" << eventId << "，帧索引:" << frameIndex;
        return false;
    }

    // 检查是否已经寻峰
    if (massData->getHasFindPeak())
    {
        qDebug() << "FileDataManager::performPeakFindingForMassData: MASS数据已完成寻峰，跳过";
        return true;
    }

    // 检查是否为MRM类型的数据，MRM数据不需要寻峰
    if (massData->shouldUseBarChart())
    {
        qDebug() << "FileDataManager::performPeakFindingForMassData: MRM类型的MASS数据，跳过寻峰";
        massData->setHasFindPeak(true);
        return true;
    }

    try
    {
        // 🎯 数据验证：检查数据完整性
        QVector<double> dataX = massData->getDataX();
        QVector<double> dataY = massData->getDataY();

        // 🎯 边界检查：验证数据大小
        if (dataX.isEmpty() || dataY.isEmpty())
        {
            qDebug() << "FileDataManager::performPeakFindingForMassData: MASS数据为空，跳过寻峰";
            massData->setHasFindPeak(true);
            return true; // 空数据不算失败
        }

        if (dataX.size() != dataY.size())
        {
            qDebug() << "FileDataManager::performPeakFindingForMassData: X和Y数据大小不匹配，跳过寻峰"
                     << "，X大小:" << dataX.size() << "，Y大小:" << dataY.size();
            massData->setHasFindPeak(true);
            return true; // 数据不匹配不算失败
        }

        if (dataX.size() < 3)
        {
            qDebug() << "FileDataManager::performPeakFindingForMassData: 数据点太少，无法寻峰"
                     << "，数据点数:" << dataX.size();
            massData->setHasFindPeak(true);
            return true; // 数据点太少不算失败
        }

        // 使用默认参数进行寻峰
        std::vector<double> originalData = dataY.toStdVector();
        std::vector<double> correctedData = cubicBaselineCorrection(movingAverage(originalData, 2));

        // 调用寻峰算法
        massData->peakVec = searchPeaks(originalData);

        // 🎯 安全的峰数据处理：添加边界检查
        for (int i = 0; i < massData->peakVec.size(); i++)
        {
            Peak &p = massData->peakVec[i];

            // 验证峰索引的有效性
            if (p.top < 0 || p.top >= dataX.size() || p.top >= dataY.size())
            {
                qDebug() << "FileDataManager::performPeakFindingForMassData: 峰顶索引越界，峰" << i << "，top:" << p.top
                         << "，数据大小:" << dataX.size();
                continue;
            }
            if (p.start < 0 || p.start >= dataX.size() || p.start >= dataY.size())
            {
                qDebug() << "FileDataManager::performPeakFindingForMassData: 峰起始索引越界，峰" << i << "，start:" << p.start
                         << "，数据大小:" << dataX.size();
                continue;
            }
            if (p.end < 0 || p.end >= dataX.size() || p.end >= dataY.size())
            {
                qDebug() << "FileDataManager::performPeakFindingForMassData: 峰结束索引越界，峰" << i << "，end:" << p.end
                         << "，数据大小:" << dataX.size();
                continue;
            }

            // 安全地设置峰点坐标
            p.pTop = QPointF(dataX.at(p.top), dataY.at(p.top));
            p.pStart = QPointF(dataX.at(p.start), dataY.at(p.start));
            p.pEnd = QPointF(dataX.at(p.end), dataY.at(p.end));
        }

        // 计算SNR
        if (!massData->peakVec.empty())
        {
            std::vector<double> snrList = CalcSNRofPeakList(correctedData, massData->peakVec, 10);
            for (int i = 0; i < massData->peakVec.size() && i < snrList.size(); i++)
            {
                massData->peakVec[i].snr = snrList[i];
            }
        }

        // 标记为已寻峰
        massData->setHasFindPeak(true);

        qDebug() << "FileDataManager::performPeakFindingForMassData: MASS数据寻峰完成，峰数量:" << massData->peakVec.size();
        return true;
    }
    catch (const std::exception &e)
    {
        qDebug() << "FileDataManager::performPeakFindingForMassData: 寻峰过程中发生异常:" << e.what();
        massData->setHasFindPeak(true); // 标记为已处理，避免重复尝试
        return false;
    }
    catch (...)
    {
        qDebug() << "FileDataManager::performPeakFindingForMassData: 寻峰过程中发生未知异常";
        massData->setHasFindPeak(true); // 标记为已处理
        return false;
    }
}

bool FileDataManager::performPeakFindingForXicData(FileData *data, double mz)
{
    if (!data)
    {
        qDebug() << "FileDataManager::performPeakFindingForXicData: FileData指针为空";
        return false;
    }

    qDebug() << "FileDataManager::performPeakFindingForXicData: 开始为XIC数据进行寻峰，m/z:" << mz;

    bool overallSuccess = true;
    int processedCount = 0;
    int skippedCount = 0;
    int failedCount = 0;

    // 获取所有事件ID
    QList<int> eventIds = data->getAllEventIds();
    qDebug() << "FileDataManager::performPeakFindingForXicData: 找到" << eventIds.size() << "个事件";

    for (int eventId : eventIds)
    {
        // 获取XIC数据
        XicChartData *xicData = data->getXicData(eventId, mz);
        if (!xicData)
        {
            qDebug() << "FileDataManager::performPeakFindingForXicData: 找不到XIC数据，事件ID:" << eventId << "，m/z:" << mz;
            skippedCount++;
            continue;
        }

        // 检查是否已经寻峰
        if (xicData->getHasFindPeak())
        {
            qDebug() << "FileDataManager::performPeakFindingForXicData: 事件ID" << eventId << "的XIC数据已完成寻峰，跳过";
            skippedCount++;
            continue;
        }

        // 检查是否为MRM类型的数据，MRM数据不需要寻峰
        if (xicData->shouldUseBarChart())
        {
            qDebug() << "FileDataManager::performPeakFindingForXicData: 事件ID" << eventId << "为MRM类型，跳过寻峰";
            xicData->setHasFindPeak(true);
            skippedCount++;
            continue;
        }

        try
        {
            // 🎯 数据验证：检查数据完整性
            QVector<double> dataX = xicData->getDataX();
            QVector<double> dataY = xicData->getDataY();

            // 🎯 边界检查：验证数据大小
            if (dataX.isEmpty() || dataY.isEmpty())
            {
                qDebug() << "FileDataManager::performPeakFindingForXicData: 事件ID" << eventId << "XIC数据为空，跳过寻峰";
                xicData->setHasFindPeak(true);
                skippedCount++;
                continue;
            }

            if (dataX.size() != dataY.size())
            {
                qDebug() << "FileDataManager::performPeakFindingForXicData: 事件ID" << eventId << "X和Y数据大小不匹配，跳过寻峰"
                         << "，X大小:" << dataX.size() << "，Y大小:" << dataY.size();
                xicData->setHasFindPeak(true);
                skippedCount++;
                continue;
            }

            if (dataX.size() < 3)
            {
                qDebug() << "FileDataManager::performPeakFindingForXicData: 事件ID" << eventId << "数据点太少，无法寻峰"
                         << "，数据点数:" << dataX.size();
                xicData->setHasFindPeak(true);
                skippedCount++;
                continue;
            }

            // 使用默认参数进行寻峰
            std::vector<double> originalData = dataY.toStdVector();
            std::vector<double> correctedData = cubicBaselineCorrection(movingAverage(originalData, 2));

            // 调用寻峰算法
            xicData->peakVec = searchPeaks(originalData);

            // 🎯 安全的峰数据处理：添加边界检查
            for (int i = 0; i < xicData->peakVec.size(); i++)
            {
                Peak &p = xicData->peakVec[i];

                // 验证峰索引的有效性
                if (p.top < 0 || p.top >= dataX.size() || p.top >= dataY.size())
                {
                    qDebug() << "FileDataManager::performPeakFindingForXicData: 峰顶索引越界，峰" << i << "，top:" << p.top
                             << "，数据大小:" << dataX.size();
                    continue;
                }
                if (p.start < 0 || p.start >= dataX.size() || p.start >= dataY.size())
                {
                    qDebug() << "FileDataManager::performPeakFindingForXicData: 峰起始索引越界，峰" << i << "，start:" << p.start
                             << "，数据大小:" << dataX.size();
                    continue;
                }
                if (p.end < 0 || p.end >= dataX.size() || p.end >= dataY.size())
                {
                    qDebug() << "FileDataManager::performPeakFindingForXicData: 峰结束索引越界，峰" << i << "，end:" << p.end
                             << "，数据大小:" << dataX.size();
                    continue;
                }

                // 安全地设置峰点坐标
                p.pTop = QPointF(dataX.at(p.top), dataY.at(p.top));
                p.pStart = QPointF(dataX.at(p.start), dataY.at(p.start));
                p.pEnd = QPointF(dataX.at(p.end), dataY.at(p.end));
            }

            // 计算SNR
            if (!xicData->peakVec.empty())
            {
                std::vector<double> snrList = CalcSNRofPeakList(correctedData, xicData->peakVec, 10);
                for (int i = 0; i < xicData->peakVec.size() && i < snrList.size(); i++)
                {
                    xicData->peakVec[i].snr = snrList[i];
                }
            }

            // 标记为已寻峰
            xicData->setHasFindPeak(true);
            processedCount++;

            qDebug() << "FileDataManager::performPeakFindingForXicData: 事件ID" << eventId << "XIC数据寻峰完成，峰数量:" << xicData->peakVec.size();
        }
        catch (const std::exception &e)
        {
            qDebug() << "FileDataManager::performPeakFindingForXicData: 事件ID" << eventId << "寻峰过程中发生异常:" << e.what();
            xicData->setHasFindPeak(true); // 标记为已处理，避免重复尝试
            failedCount++;
            overallSuccess = false;
        }
        catch (...)
        {
            qDebug() << "FileDataManager::performPeakFindingForXicData: 事件ID" << eventId << "寻峰过程中发生未知异常";
            xicData->setHasFindPeak(true); // 标记为已处理
            failedCount++;
            overallSuccess = false;
        }
    }

    qDebug() << "FileDataManager::performPeakFindingForXicData: XIC寻峰完成，处理:" << processedCount
             << "，跳过:" << skippedCount << "，失败:" << failedCount;

    return overallSuccess && failedCount == 0;
}
