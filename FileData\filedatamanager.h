#ifndef FILEDATAMANAGER_H
#define FILEDATAMANAGER_H

#include <QObject>
#include "filedata.h"
#include <QDebug>
#include <QThread>
// 移除DataReader DLL依赖
// #include "datareader.h"
#include "dataprocessor.h"
#include "paramfilereader.h"
#include <QDateTime>
#include <QtMath>
#include "definecommands.h"
#include "taskmanager.h"
#include "Globals/GlobalDefine.h"
#include "LxDataReader/lxdatareader.h"
#include "avgmassmanager.h"
// 移除DataReader DLL依赖和LxCustomDataReader依赖
// #include "FileData/datareader.h"
// #include "LxDataReader/lxcustomdatareader.h"

class FileDataManager : public QObject
{
    Q_OBJECT
public:
    explicit FileDataManager(QObject *parent = nullptr);
    ~FileDataManager();

    // 存储所有创建的FileData指针，path为Param的完整路径
    static QMap<QString, FileData *> FileData_Map;

    // 构造一个FileData指针，并添加到FileData_Map
    FileData *generateFileData(QString path);

    // 扫描模式检查通过后，将FileData添加到管理器
    void confirmAddFileData(FileData *data);

    void loadMassDataForFrame(QString paramPath, const QPointF &point, int eventId);                            // 加载某帧质谱数据
    void loadMassDataForFrameWithAvg(QString paramPath, QVector<double> pointVec, int eventId, FileData *data); // 加载某帧质谱数据用作计算平均质谱
    // 加载所有Mass数据
    void loadXicData(FileData *data, double mz);
    // 设置线程池最大线程数
    void setMaxThreadCount(int count);

    // 获取线程池最大线程数
    int maxThreadCount() const;

    // 获取当前活动线程数
    int activeThreadCount() const;

    void showMassChart(QVector<std::tuple<QString, QPointF, int>> vecTuple);
    // void showXicChart(double mz); // 已删除，使用showMultipleXicChart代替

    /**
     * @brief 显示多个XIC图表
     * @param xicRequests XIC请求列表，每个元素包含(路径, 事件ID, m/z值)
     */
    void showMultipleXicChart(QVector<std::tuple<QString, int, double>> xicRequests);

public slots:
    /**
     * @brief 获取平均质谱图
     * @param 背景质谱数据x点数组，具体介绍查看LxChart.h
     */
    void getAvgMass(QVector<std::tuple<QString, int, QVector<double>>> vec);

    // TIC2XIC提取数据
    void slot_sg_TIC2XIC_Clicked(QString path);

    /**
     * @brief 处理下一个XIC请求
     */
    void processNextXicRequest();

    bool removeData(QString path);
    // 根据事件Id获取颜色（用于给MASS XIC曲线颜色同步）
    static QColor getOriginColorByEvent(QString path, int eventId);

private:
    // 数据处理对象 - 移除DLL依赖的DataReader和LxCustomDataReader
    // DataReader *dataReader;  // 已移除DLL依赖
    // LxCustomDataReader *lxCustomDataReader;  // 已集成到LxDataReader
    DataProcessor *dataProcessor;
    ParamFileReader *paramFileReader;
    LxDataReader *lxDataReader; // 统一的数据读取器

    // 用于顺序处理多个XIC请求的队列和状态 - 现在只需要mz参数
    QVector<double> m_pendingXicRequests;
    bool m_isProcessingXicRequests = false;

private:
    int totalAvgLoadCount = 0; // 平均质谱总加载数量

    /**
     * @brief 更新TIC数据的扫描模式
     * @param data FileData指针
     */
    void updateTicScanModes(FileData *data);

    /**
     * @brief 🎯 对FileData进行寻峰处理
     * @param data FileData指针
     * @return 寻峰是否成功
     */
    bool performPeakFindingForFileData(FileData *data);

    /**
     * @brief 🎯 对MASS数据进行寻峰处理
     * @param data FileData指针
     * @param eventId 事件ID
     * @param frameIndex 帧索引
     * @return 寻峰是否成功
     */
    bool performPeakFindingForMassData(FileData *data, int eventId, int frameIndex);

    /**
     * @brief 🎯 对XIC数据进行寻峰处理
     * @param data FileData指针
     * @param mz m/z值
     * @return 寻峰是否成功
     */
    bool performPeakFindingForXicData(FileData *data, double mz);

signals:
    /**
     * @brief 发送图表数据加载成功信号
     * @param data 数据文件指针
     */
    void sg_sendTicChartData(FileData *data);
    void sg_sendMassChartData(int eventId, qint64 frameIndex, FileData *data);
    void sg_sendXicChartData(FileData *data);
    void sg_sendIndependentXicChartData(XicChartData *xicData); // 新增：发送独立的XIC数据
    void sg_readFileFinished(bool success, const QString &filePath);
    void sg_massFrameReady(bool success, const QString &filePath, int frameIndex);

    /**
     * @brief sg_xicLoadSuccess 加载xic数据是否成功信号
     * @param flag 是否读取成功 true成功 false失败
     * @param data 读取XIC的FileData类
     * <AUTHOR>
     */
    void sg_xicLoadSuccess(bool flag, FileData &data);

    /**
     * @brief XIC处理完成信号，用于触发下一个XIC请求处理
     */
    void sg_xicProcessingCompleted();

    /**
     * @brief 开始批量添加信号
     */
    void sg_beginBatchAdd();

    /**
     * @brief 结束批量添加信号
     */
    void sg_endBatchAdd();

    /**
     * @brief 开始MASS批量加载信号
     * @param expectedCount 预期的MASS数量
     */
    void sg_startMassBatchLoading(int expectedCount);

    /**
     * @brief 🎯 寻峰失败警告信号
     * @param filePath 文件路径
     * @param dataType 数据类型（TIC/MASS/XIC）
     * @param errorMessage 错误信息
     */
    void sg_peakFindingFailed(const QString &filePath, const QString &dataType, const QString &errorMessage);
};

#endif // FILEDATAMANAGER_H
